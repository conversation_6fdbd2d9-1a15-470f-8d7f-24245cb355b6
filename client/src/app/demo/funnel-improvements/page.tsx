'use client';

import React from 'react';
import { Funnel<PERSON>hart } from '@/components/FunnelChart';
import { FunnelData } from '@/types/analytics';

// Demo data to showcase the improvements
const demoData: FunnelData = {
  'Application Submitted': { count: 1250, percentage: 100 },
  'Documents Verified': { count: 1100, percentage: 88 },
  'University Applied': { count: 950, percentage: 76 },
  'Offer Received': { count: 720, percentage: 57.6 },
  'Visa Applied': { count: 650, percentage: 52 },
  'Visa Approved': { count: 580, percentage: 46.4 },
  'Travel Completed': { count: 520, percentage: 41.6 }
};

const smallSegmentData: FunnelData = {
  'Major Segment': { count: 1000, percentage: 95.2 },
  'Minor Segment': { count: 30, percentage: 2.9 },
  'Tiny Segment': { count: 20, percentage: 1.9 }
};

const colorSchemes = ['blue', 'green', 'purple', 'orange', 'red'] as const;

export default function FunnelImprovementsDemo() {
  const handleSegmentClick = (segment: string, data: any) => {
    alert(`Clicked: ${segment}\nCount: ${data.count}\nPercentage: ${data.percentage}%`);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            🎉 Funnel Chart Improvements Demo
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Showcasing the enhanced UI/UX improvements including proper hover states, 
            accessibility features, responsive design, and visual feedback.
          </p>
        </div>

        {/* Improvements Highlights */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">✨ Key Improvements</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 mb-2">🎯 Fixed Hover States</h3>
              <p className="text-sm text-green-700">
                Hover effects now properly highlight the colored bars with brightness and saturation changes
              </p>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-800 mb-2">♿ Accessibility Enhanced</h3>
              <p className="text-sm text-blue-700">
                Added ARIA labels, keyboard navigation, focus states, and screen reader support
              </p>
            </div>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h3 className="font-semibold text-purple-800 mb-2">📱 Responsive Design</h3>
              <p className="text-sm text-purple-700">
                Improved mobile experience with better text sizing and spacing
              </p>
            </div>
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <h3 className="font-semibold text-orange-800 mb-2">🎨 Visual Feedback</h3>
              <p className="text-sm text-orange-700">
                Added subtle animations, scale effects, and better visual hierarchy
              </p>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="font-semibold text-red-800 mb-2">🔢 Better Formatting</h3>
              <p className="text-sm text-red-700">
                Large numbers now display with proper comma formatting
              </p>
            </div>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-2">🎯 Small Segments</h3>
              <p className="text-sm text-gray-700">
                Tiny segments now show progress indicators for better visibility
              </p>
            </div>
          </div>
        </div>

        {/* Interactive Demo */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">🖱️ Interactive Demo</h2>
          <p className="text-gray-600 mb-6">
            Try hovering over the segments below and clicking them to see the improvements in action!
          </p>
          
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <FunnelChart
              title="Student Journey Funnel (Click to interact!)"
              data={demoData}
              colorScheme="blue"
              onSegmentClick={handleSegmentClick}
              height={400}
            />
            
            <FunnelChart
              title="Small Segments Demo"
              data={smallSegmentData}
              colorScheme="green"
              onSegmentClick={handleSegmentClick}
              height={400}
            />
          </div>
        </div>

        {/* Color Schemes Showcase */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">🎨 Color Schemes</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {colorSchemes.map((scheme, index) => (
              <FunnelChart
                key={scheme}
                title={`${scheme.charAt(0).toUpperCase() + scheme.slice(1)} Theme`}
                data={{
                  'High': { count: 100, percentage: 50 },
                  'Medium': { count: 75, percentage: 37.5 },
                  'Low': { count: 25, percentage: 12.5 }
                }}
                colorScheme={scheme}
                onSegmentClick={handleSegmentClick}
                height={280}
              />
            ))}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-blue-800 mb-4">📋 How to Test</h2>
          <ul className="space-y-2 text-blue-700">
            <li className="flex items-start">
              <span className="font-semibold mr-2">1.</span>
              <span><strong>Hover Effects:</strong> Move your mouse over any segment to see the improved hover states</span>
            </li>
            <li className="flex items-start">
              <span className="font-semibold mr-2">2.</span>
              <span><strong>Click Interaction:</strong> Click on any segment to see the click handler in action</span>
            </li>
            <li className="flex items-start">
              <span className="font-semibold mr-2">3.</span>
              <span><strong>Keyboard Navigation:</strong> Use Tab to navigate and Enter/Space to activate segments</span>
            </li>
            <li className="flex items-start">
              <span className="font-semibold mr-2">4.</span>
              <span><strong>Responsive Design:</strong> Resize your browser window to see mobile adaptations</span>
            </li>
            <li className="flex items-start">
              <span className="font-semibold mr-2">5.</span>
              <span><strong>Small Segments:</strong> Notice the progress dots for tiny segments in the second chart</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
