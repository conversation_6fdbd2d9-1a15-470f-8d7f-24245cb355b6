import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { FunnelChart } from '../FunnelChart';
import { FunnelData } from '@/types/analytics';

const mockData: FunnelData = {
  'High Priority': { count: 150, percentage: 45.5 },
  'Medium Priority': { count: 100, percentage: 30.3 },
  'Low Priority': { count: 80, percentage: 24.2 }
};

describe('FunnelChart', () => {
  it('renders chart title correctly', () => {
    render(
      <FunnelChart
        title="Test Funnel Chart"
        data={mockData}
        colorScheme="blue"
      />
    );
    
    expect(screen.getByText('Test Funnel Chart')).toBeInTheDocument();
  });

  it('displays all segments with correct data', () => {
    render(
      <FunnelChart
        title="Test Chart"
        data={mockData}
        colorScheme="blue"
        showPercentages={true}
      />
    );
    
    // Check if all segments are rendered
    expect(screen.getByText('High Priority')).toBeInTheDocument();
    expect(screen.getByText('Medium Priority')).toBeInTheDocument();
    expect(screen.getByText('Low Priority')).toBeInTheDocument();
    
    // Check if counts are displayed with proper formatting
    expect(screen.getByText('150')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
    expect(screen.getByText('80')).toBeInTheDocument();
    
    // Check if percentages are displayed
    expect(screen.getByText('(45.5%)')).toBeInTheDocument();
    expect(screen.getByText('(30.3%)')).toBeInTheDocument();
    expect(screen.getByText('(24.2%)')).toBeInTheDocument();
  });

  it('handles click events when onSegmentClick is provided', () => {
    const mockOnClick = jest.fn();
    
    render(
      <FunnelChart
        title="Test Chart"
        data={mockData}
        colorScheme="blue"
        onSegmentClick={mockOnClick}
      />
    );
    
    // Find clickable segment and click it
    const highPrioritySegment = screen.getByRole('button', { 
      name: /High Priority: 150 items \(45.5%\)/ 
    });
    
    fireEvent.click(highPrioritySegment);
    
    expect(mockOnClick).toHaveBeenCalledWith('High Priority', {
      count: 150,
      percentage: 45.5
    });
  });

  it('supports keyboard navigation when clickable', () => {
    const mockOnClick = jest.fn();
    
    render(
      <FunnelChart
        title="Test Chart"
        data={mockData}
        colorScheme="blue"
        onSegmentClick={mockOnClick}
      />
    );
    
    const segment = screen.getByRole('button', { 
      name: /High Priority: 150 items \(45.5%\)/ 
    });
    
    // Test Enter key
    fireEvent.keyDown(segment, { key: 'Enter' });
    expect(mockOnClick).toHaveBeenCalledWith('High Priority', {
      count: 150,
      percentage: 45.5
    });
    
    // Test Space key
    fireEvent.keyDown(segment, { key: ' ' });
    expect(mockOnClick).toHaveBeenCalledTimes(2);
  });

  it('renders empty state when no data provided', () => {
    render(
      <FunnelChart
        title="Empty Chart"
        data={{}}
        colorScheme="blue"
      />
    );
    
    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('applies correct color scheme', () => {
    const { container } = render(
      <FunnelChart
        title="Test Chart"
        data={mockData}
        colorScheme="green"
      />
    );
    
    // Check if title has green text color
    const title = screen.getByText('Test Chart');
    expect(title).toHaveClass('text-green-700');
  });

  it('handles large numbers with proper formatting', () => {
    const largeData: FunnelData = {
      'Large Number': { count: 1234567, percentage: 100 }
    };
    
    render(
      <FunnelChart
        title="Large Numbers"
        data={largeData}
        colorScheme="blue"
      />
    );
    
    // Check if large numbers are formatted with commas
    expect(screen.getByText('1,234,567')).toBeInTheDocument();
  });

  it('shows progress indicator for very small segments', () => {
    const smallData: FunnelData = {
      'Large Segment': { count: 1000, percentage: 95 },
      'Tiny Segment': { count: 1, percentage: 5 }
    };
    
    const { container } = render(
      <FunnelChart
        title="Small Segments"
        data={smallData}
        colorScheme="blue"
      />
    );
    
    // Very small segments should have a progress indicator dot
    const progressDots = container.querySelectorAll('.w-2.h-2.rounded-full');
    expect(progressDots.length).toBeGreaterThan(0);
  });
});
