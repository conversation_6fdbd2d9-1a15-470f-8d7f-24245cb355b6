# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/client/node_modules
/client/package-lock.json
/node_modules
/package-lock.json
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

__pycache__/
*.pyc
*.pyo
*.pyd

server/__pycache__/
*.pyc

/client/.next/
/client/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)


# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts


/server/.venv